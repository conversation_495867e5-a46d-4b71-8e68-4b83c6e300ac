"""Data loading utilities for causal language modeling training."""

import os
import json
import random
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass

import torch
from torch.utils.data import Dataset, DataLoader, DistributedSampler
from transformers import PreTrainedTokenizer
import torch.distributed as dist


class CausalLMDataset(Dataset):
    """Dataset for causal language modeling training."""
    
    def __init__(
        self,
        data_path: str,
        tokenizer: PreTrainedTokenizer,
        max_length: int = 2048,
        split: str = "train",
    ):
        """
        Initialize the dataset.
        
        Args:
            data_path: Path to the dataset file (JSON lines format)
            tokenizer: Tokenizer to use for encoding text
            max_length: Maximum sequence length
            split: Dataset split ("train" or "eval")
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.split = split
        
        # Load data
        self.examples = self._load_data(data_path)
        
        # Set pad token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def _load_data(self, data_path: str) -> List[Dict[str, Any]]:
        """Load data from file."""
        examples = []
        
        if data_path.endswith('.jsonl'):
            with open(data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    examples.append(json.loads(line.strip()))
        elif data_path.endswith('.json'):
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    examples = data
                else:
                    examples = [data]
        else:
            raise ValueError(f"Unsupported file format: {data_path}")
        
        return examples
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single example."""
        example = self.examples[idx]
        
        # Extract text - support different formats
        if "text" in example:
            text = example["text"]
        elif "content" in example:
            text = example["content"]
        elif "input" in example and "output" in example:
            text = example["input"] + example["output"]
        else:
            raise ValueError(f"Cannot find text field in example: {example.keys()}")
        
        # Tokenize
        encoded = self.tokenizer(
            text,
            truncation=True,
            max_length=self.max_length,
            padding=False,
            return_tensors="pt"
        )
        
        input_ids = encoded["input_ids"].squeeze(0)
        attention_mask = encoded["attention_mask"].squeeze(0)
        
        # For causal LM, labels are the same as input_ids
        labels = input_ids.clone()
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels,
        }


@dataclass
class CausalLMDataCollator:
    """Data collator for causal language modeling."""
    
    tokenizer: PreTrainedTokenizer
    max_length: int = 2048
    pad_to_multiple_of: Optional[int] = None
    
    def __call__(self, examples: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """Collate examples into a batch."""
        batch_size = len(examples)
        
        # Get maximum length in batch
        max_len = max(len(ex["input_ids"]) for ex in examples)
        if self.pad_to_multiple_of:
            max_len = ((max_len + self.pad_to_multiple_of - 1) // self.pad_to_multiple_of) * self.pad_to_multiple_of
        max_len = min(max_len, self.max_length)
        
        # Initialize batch tensors
        input_ids = torch.full((batch_size, max_len), self.tokenizer.pad_token_id, dtype=torch.long)
        attention_mask = torch.zeros((batch_size, max_len), dtype=torch.long)
        labels = torch.full((batch_size, max_len), -100, dtype=torch.long)
        
        # Fill batch tensors
        for i, example in enumerate(examples):
            seq_len = min(len(example["input_ids"]), max_len)
            input_ids[i, :seq_len] = example["input_ids"][:seq_len]
            attention_mask[i, :seq_len] = example["attention_mask"][:seq_len]
            labels[i, :seq_len] = example["labels"][:seq_len]
        
        return {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels,
        }


def create_dataloader(
    dataset: Dataset,
    batch_size: int,
    collate_fn: Optional[Any] = None,
    shuffle: bool = True,
    num_workers: int = 0,
    pin_memory: bool = True,
    drop_last: bool = True,
    distributed: bool = False,
) -> DataLoader:
    """Create a DataLoader with optional distributed sampling."""
    
    sampler = None
    if distributed and dist.is_initialized():
        sampler = DistributedSampler(
            dataset,
            shuffle=shuffle,
            drop_last=drop_last,
        )
        shuffle = False  # Sampler handles shuffling
    
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        sampler=sampler,
        collate_fn=collate_fn,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=drop_last,
    )


def prepare_datasets(
    data_path: str,
    tokenizer: PreTrainedTokenizer,
    max_length: int = 2048,
    train_split_ratio: float = 0.9,
) -> tuple[CausalLMDataset, Optional[CausalLMDataset]]:
    """
    Prepare train and eval datasets.
    
    Args:
        data_path: Path to dataset file
        tokenizer: Tokenizer to use
        max_length: Maximum sequence length
        train_split_ratio: Ratio of data to use for training
        
    Returns:
        Tuple of (train_dataset, eval_dataset)
    """
    # Load full dataset
    full_dataset = CausalLMDataset(data_path, tokenizer, max_length)
    
    if train_split_ratio >= 1.0:
        return full_dataset, None
    
    # Split dataset
    total_size = len(full_dataset)
    train_size = int(total_size * train_split_ratio)
    
    # Create indices for splitting
    indices = list(range(total_size))
    random.shuffle(indices)
    
    train_indices = indices[:train_size]
    eval_indices = indices[train_size:]
    
    # Create subset datasets
    train_examples = [full_dataset.examples[i] for i in train_indices]
    eval_examples = [full_dataset.examples[i] for i in eval_indices]
    
    train_dataset = CausalLMDataset.__new__(CausalLMDataset)
    train_dataset.tokenizer = tokenizer
    train_dataset.max_length = max_length
    train_dataset.split = "train"
    train_dataset.examples = train_examples
    
    eval_dataset = CausalLMDataset.__new__(CausalLMDataset)
    eval_dataset.tokenizer = tokenizer
    eval_dataset.max_length = max_length
    eval_dataset.split = "eval"
    eval_dataset.examples = eval_examples
    
    return train_dataset, eval_dataset
