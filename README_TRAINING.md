# Nano-vLLM Training Guide: Complete Usage Manual

This comprehensive guide covers everything you need to know about training language models with nano-vllm, including the new multi-document training capabilities.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Installation](#installation)
3. [Data Preparation](#data-preparation)
4. [Single Document Training](#single-document-training)
5. [Multi-Document Training](#multi-document-training)
6. [Configuration](#configuration)
7. [Advanced Features](#advanced-features)
8. [Distributed Training](#distributed-training)
9. [Memory Optimization](#memory-optimization)
10. [Troubleshooting](#troubleshooting)
11. [Examples](#examples)

## Quick Start

### 30-Second Training Setup

```bash
# 1. Install nano-vllm with training dependencies
pip install torch transformers datasets accelerate flash-attn

# 2. Prepare your data (any of these formats work)
mkdir my_documents
echo "Your training text here..." > my_documents/doc1.txt
echo '{"text": "More training data..."}' > my_documents/data.jsonl

# 3. Create basic config
cat > config.json << EOF
{
  "model_name_or_path": "Qwen/Qwen3-0.6B",
  "dataset_path": "./my_documents",
  "output_dir": "./checkpoints",
  "learning_rate": 5e-5,
  "per_device_train_batch_size": 2,
  "num_train_epochs": 3
}
EOF

# 4. Start training
python train.py --config config.json --use_multi_document
```

## Installation

### Basic Installation
```bash
pip install torch transformers datasets accelerate
```

### With Optimizations
```bash
pip install torch transformers datasets accelerate
pip install flash-attn  # For attention optimization
pip install triton      # For custom kernels
```

### Development Installation
```bash
git clone https://github.com/your-repo/nano-vllm
cd nano-vllm
pip install -e .
```

## Data Preparation

### Supported Data Formats

Nano-vLLM supports multiple data formats for maximum flexibility:

#### 1. Plain Text Files (`.txt`)
```
documents/
├── chapter1.txt
├── chapter2.txt
└── appendix.txt
```

Each `.txt` file is treated as a single document.

#### 2. JSON Lines (`.jsonl`)
```jsonl
{"text": "First training example..."}
{"text": "Second training example..."}
{"content": "Alternative field name works too"}
{"input": "Question", "output": "Answer"}
```

#### 3. JSON Files (`.json`)
```json
[
  {"text": "Document 1 content..."},
  {"text": "Document 2 content..."}
]
```

Or single document:
```json
{"text": "Single document content..."}
```

#### 4. Mixed Collections
```
my_training_data/
├── books/
│   ├── book1.txt
│   └── book2.txt
├── articles.jsonl
├── papers.json
└── notes/
    ├── meeting_notes.txt
    └── research_ideas.jsonl
```

### Data Quality Guidelines

1. **Text Quality**: Ensure clean, well-formatted text
2. **Encoding**: Use UTF-8 encoding for all files
3. **Size**: No strict limits, but consider memory constraints
4. **Structure**: Consistent field names across JSON files

## Single Document Training

### Basic Single File Training

For traditional single-file training:

```python
from nanovllm import Trainer, TrainingConfig

config = TrainingConfig(
    model_name_or_path="Qwen/Qwen3-0.6B",
    dataset_path="data/train.jsonl",  # Single file
    output_dir="./checkpoints",
    learning_rate=5e-5,
    per_device_train_batch_size=2,
    num_train_epochs=3,
)

trainer = Trainer.from_pretrained(config, tokenizer)
trainer.train()
```

### Command Line
```bash
python train.py --config single_file_config.json
```

## Multi-Document Training

### Overview

Multi-document training allows you to train on entire collections of documents with intelligent chunking and processing strategies.

### Basic Multi-Document Setup

```python
from nanovllm.training import MultiDocumentDataset, analyze_document_collection

# 1. Analyze your documents first
analysis = analyze_document_collection("./documents")
print(f"Found {analysis['total_documents']} documents")
print(f"File types: {analysis['file_types']}")
print(f"Total characters: {analysis['total_characters']:,}")

# 2. Create dataset
dataset = MultiDocumentDataset(
    data_path="./documents",
    tokenizer=tokenizer,
    max_length=2048,
    concatenate_documents=False,  # Process individually
    chunk_overlap=128,
    min_chunk_size=100,
)

print(f"Created {len(dataset)} training chunks")
```

### Processing Strategies

#### Strategy 1: Individual Document Processing
Best for maintaining document boundaries and structure.

```python
dataset = MultiDocumentDataset(
    data_path="./documents",
    tokenizer=tokenizer,
    concatenate_documents=False,  # Keep documents separate
    chunk_overlap=50,             # Small overlap
    min_chunk_size=100,          # Minimum viable chunk
)
```

**Use when:**
- Documents have distinct topics/styles
- You want to preserve document structure
- Training on diverse content types

#### Strategy 2: Concatenated Processing
Best for creating continuous training sequences.

```python
dataset = MultiDocumentDataset(
    data_path="./documents",
    tokenizer=tokenizer,
    concatenate_documents=True,        # Combine all documents
    document_separator="\n\n---\n\n",  # Clear separator
    chunk_overlap=128,                 # Larger overlap
)
```

**Use when:**
- Documents are similar in style/topic
- You want maximum context length
- Training on homogeneous content

### Command Line Multi-Document Training

```bash
# Basic multi-document training
python train.py --config config.json --use_multi_document

# With document analysis
python train.py --config config.json --use_multi_document --analyze_documents

# With concatenation
python train.py --config config.json --use_multi_document --concatenate_documents

# Custom chunk overlap
python train.py --config config.json --use_multi_document --chunk_overlap 256
```

### Advanced Multi-Document Features

#### Document Analysis
```python
from nanovllm.training import analyze_document_collection

analysis = analyze_document_collection("./documents")

# Detailed breakdown
for file_info in analysis['files']:
    print(f"{file_info['path']}:")
    print(f"  Documents: {file_info['documents']}")
    print(f"  Characters: {file_info['characters']:,}")
    print(f"  Avg length: {file_info['avg_length']:.1f}")
```

#### Custom Text Extraction
The system automatically handles various field names:
- `text`, `content`, `body` for main content
- `input` + `output` for Q&A pairs
- Automatic concatenation of string fields

#### Chunk Metadata
Each training chunk includes metadata:
```python
sample = dataset[0]
metadata = sample['metadata']
print(f"Source: {metadata['source_file']}")
print(f"Chunk: {metadata['chunk_index']}")
print(f"Tokens: {metadata['token_count']}")
```

## Configuration

### Complete Configuration Reference

```json
{
  "model_name_or_path": "Qwen/Qwen3-0.6B",
  "dataset_path": "./documents",
  "output_dir": "./checkpoints",
  
  "learning_rate": 5e-5,
  "weight_decay": 0.01,
  "beta1": 0.9,
  "beta2": 0.95,
  
  "per_device_train_batch_size": 2,
  "gradient_accumulation_steps": 4,
  "max_seq_length": 2048,
  
  "num_train_epochs": 3,
  "warmup_steps": 100,
  "lr_scheduler_type": "cosine",
  
  "mixed_precision": "bf16",
  "gradient_clipping": 1.0,
  "gradient_checkpointing": true,
  
  "eval_steps": 500,
  "save_steps": 1000,
  "logging_steps": 10,
  
  "seed": 42
}
```

## Advanced Features

### Memory Optimization

#### Gradient Checkpointing
```python
config = TrainingConfig(
    gradient_checkpointing=True,  # Reduces memory usage
    mixed_precision="bf16",       # Further memory savings
)
```

#### Memory Estimation
```python
from nanovllm.training.memory import estimate_training_memory

memory_estimate = estimate_training_memory(
    model=model,
    batch_size=2,
    sequence_length=2048,
    gradient_checkpointing=True,
    mixed_precision=True,
)

print(f"Estimated memory: {memory_estimate['total_gb']:.2f}GB")
```

### Custom Training Loops

```python
from nanovllm.training import create_optimizer, create_scheduler

# Custom optimizer
optimizer = create_optimizer(
    model=model,
    learning_rate=1e-4,
    weight_decay=0.01,
)

# Custom scheduler
scheduler = create_scheduler(
    optimizer=optimizer,
    scheduler_type="cosine",
    num_warmup_steps=100,
    num_training_steps=1000,
)

# Training loop
for batch in dataloader:
    outputs = model(**batch)
    loss = outputs["loss"]
    loss.backward()
    optimizer.step()
    scheduler.step()
    optimizer.zero_grad()
```

### Evaluation During Training

```python
from nanovllm.training.evaluation import evaluate_model

# Evaluate model
metrics = evaluate_model(
    model=model,
    tokenizer=tokenizer,
    eval_dataloader=eval_dataloader,
    device=device,
)

print(f"Perplexity: {metrics.perplexity:.2f}")
print(f"Loss: {metrics.loss:.4f}")
```

### Checkpoint Management

```python
from nanovllm.training.checkpoint import CheckpointManager

# Advanced checkpoint management
checkpoint_manager = CheckpointManager(
    output_dir="./checkpoints",
    save_total_limit=5,
)

# Save with metadata
checkpoint_manager.save_checkpoint(
    model=model,
    optimizer=optimizer,
    scheduler=scheduler,
    tokenizer=tokenizer,
    step=1000,
    epoch=1,
    loss=2.5,
    config=config,
    is_best=True,
)
```

## Distributed Training

### Tensor Parallelism

Split model layers across GPUs:

```json
{
  "tensor_parallel_size": 4,
  "per_device_train_batch_size": 1
}
```

```bash
torchrun --nproc_per_node=4 train.py --config config.json
```

### Data Parallelism

Replicate model across GPUs:

```bash
torchrun --nproc_per_node=4 train.py --config config.json
```

### Mixed Parallelism

Combine tensor and data parallelism:

```json
{
  "tensor_parallel_size": 2,
  "data_parallel_size": 2
}
```

```bash
torchrun --nproc_per_node=4 train.py --config config.json
```

## Memory Optimization

### Strategies by Model Size

#### Small Models (< 1B parameters)
```json
{
  "per_device_train_batch_size": 4,
  "gradient_accumulation_steps": 2,
  "mixed_precision": "bf16"
}
```

#### Medium Models (1B - 7B parameters)
```json
{
  "per_device_train_batch_size": 2,
  "gradient_accumulation_steps": 4,
  "mixed_precision": "bf16",
  "gradient_checkpointing": true
}
```

#### Large Models (> 7B parameters)
```json
{
  "per_device_train_batch_size": 1,
  "gradient_accumulation_steps": 8,
  "mixed_precision": "bf16",
  "gradient_checkpointing": true,
  "tensor_parallel_size": 4
}
```

### Memory Monitoring

```python
from nanovllm.training.memory import MemoryManager

memory_manager = MemoryManager(device)

with memory_manager.memory_profiling("training_step"):
    # Your training code here
    pass

memory_manager.log_memory_usage("After training step")
```

## Troubleshooting

### Common Issues and Solutions

#### Out of Memory (OOM)
```bash
# Reduce batch size
--per_device_train_batch_size 1

# Increase gradient accumulation
--gradient_accumulation_steps 8

# Enable optimizations
--gradient_checkpointing --mixed_precision bf16
```

#### Slow Training
```bash
# Increase batch size if memory allows
--per_device_train_batch_size 4

# Use mixed precision
--mixed_precision bf16

# Multiple workers for data loading
--dataloader_num_workers 4
```

#### Poor Convergence
```bash
# Adjust learning rate
--learning_rate 1e-5

# Add warmup
--warmup_steps 100

# Check gradient clipping
--gradient_clipping 1.0
```

#### Multi-Document Issues

**Problem**: Documents not loading
```bash
# Check file formats and permissions
python -c "from nanovllm.training import analyze_document_collection; print(analyze_document_collection('./documents'))"
```

**Problem**: Chunks too small/large
```python
# Adjust chunking parameters
dataset = MultiDocumentDataset(
    chunk_overlap=64,      # Reduce overlap
    min_chunk_size=50,     # Lower minimum
    max_length=1024,       # Smaller chunks
)
```

## Examples

### Example 1: Training on Research Papers

```python
# Analyze papers first
analysis = analyze_document_collection("./research_papers")
print(f"Found {analysis['total_documents']} papers")

# Configure for academic content
config = TrainingConfig(
    model_name_or_path="Qwen/Qwen3-1.8B",
    dataset_path="./research_papers",
    output_dir="./paper_model",

    # Academic content settings
    max_seq_length=4096,        # Long context for papers
    concatenate_documents=False, # Keep paper boundaries
    chunk_overlap=256,          # Good overlap for continuity

    # Training settings
    learning_rate=3e-5,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=8,
    num_train_epochs=2,

    # Optimization
    mixed_precision="bf16",
    gradient_checkpointing=True,
)

# Train
trainer = Trainer.from_pretrained(config, tokenizer)
trainer.train()
```

### Example 2: Training on Code Documentation

```python
config = TrainingConfig(
    model_name_or_path="Qwen/Qwen3-0.6B",
    dataset_path="./code_docs",
    output_dir="./code_model",

    # Code-specific settings
    max_seq_length=2048,
    concatenate_documents=True,   # Combine related docs
    document_separator="\n\n# NEW DOCUMENT #\n\n",
    chunk_overlap=100,

    # Fast training for code
    learning_rate=5e-5,
    per_device_train_batch_size=2,
    gradient_accumulation_steps=4,
    num_train_epochs=3,
)
```

### Example 3: Training on Mixed Content

```python
# For diverse content types
config = TrainingConfig(
    dataset_path="./mixed_content",

    # Preserve document diversity
    concatenate_documents=False,
    chunk_overlap=64,
    min_chunk_size=100,

    # Robust training settings
    learning_rate=2e-5,
    warmup_steps=200,
    lr_scheduler_type="cosine",
    gradient_clipping=1.0,
)
```

### Example 4: Distributed Multi-Document Training

```bash
# Multi-node, multi-document training
torchrun \
  --nproc_per_node=8 \
  --nnodes=2 \
  --master_addr=node1 \
  --master_port=12345 \
  train.py \
  --config distributed_config.json \
  --use_multi_document \
  --concatenate_documents \
  --chunk_overlap 256
```

## Best Practices

### Data Preparation
1. **Clean your data**: Remove corrupted files, fix encoding issues
2. **Analyze first**: Always run document analysis before training
3. **Test chunking**: Verify chunk sizes work with your model
4. **Consistent format**: Use consistent field names across JSON files

### Training Configuration
1. **Start small**: Begin with smaller models and datasets
2. **Monitor memory**: Use memory profiling to optimize settings
3. **Save frequently**: Use reasonable save_steps for long training
4. **Evaluate regularly**: Set appropriate eval_steps

### Multi-Document Strategy
1. **Individual processing**: For diverse content types
2. **Concatenated processing**: For similar, related content
3. **Appropriate overlap**: Balance context and efficiency
4. **Metadata tracking**: Use chunk metadata for debugging

### Performance Optimization
1. **Mixed precision**: Always use bf16 on modern GPUs
2. **Gradient checkpointing**: For memory-constrained training
3. **Batch size tuning**: Find the sweet spot for your hardware
4. **Data loading**: Use multiple workers when possible

## Getting Help

### Debug Information
```python
# Check your setup
from nanovllm.training import analyze_document_collection
analysis = analyze_document_collection("./your_data")
print(analysis)

# Memory estimation
from nanovllm.training.memory import estimate_training_memory
estimate = estimate_training_memory(model, batch_size=2, sequence_length=2048)
print(f"Estimated memory: {estimate['total_gb']:.2f}GB")

# Test dataset creation
dataset = MultiDocumentDataset("./your_data", tokenizer, max_length=512)
print(f"Created {len(dataset)} chunks from {len(dataset.documents)} documents")
```

### Community Resources
- GitHub Issues: Report bugs and request features
- Documentation: Comprehensive guides and API reference
- Examples: Working code examples for common scenarios

---

**Happy Training with Nano-vLLM! 🚀**

This guide covers everything you need to train language models on single files or entire document collections. Start with the quick start section and gradually explore advanced features as needed.

### Multi-Document Specific Options

```json
{
  "_comment": "Multi-document training options",
  "use_multi_document": true,
  "concatenate_documents": false,
  "document_separator": "\n\n---\n\n",
  "chunk_overlap": 128,
  "min_chunk_size": 100,
  "analyze_documents": true
}
```

### Configuration Profiles

#### Profile 1: Small Model, Limited Memory
```json
{
  "model_name_or_path": "Qwen/Qwen3-0.6B",
  "per_device_train_batch_size": 1,
  "gradient_accumulation_steps": 8,
  "max_seq_length": 1024,
  "mixed_precision": "bf16",
  "gradient_checkpointing": true
}
```

#### Profile 2: Large Model, High Memory
```json
{
  "model_name_or_path": "Qwen/Qwen3-7B",
  "per_device_train_batch_size": 4,
  "gradient_accumulation_steps": 2,
  "max_seq_length": 4096,
  "mixed_precision": "bf16",
  "tensor_parallel_size": 4
}
```

#### Profile 3: Multi-Document Research Papers
```json
{
  "dataset_path": "./research_papers",
  "concatenate_documents": false,
  "chunk_overlap": 256,
  "min_chunk_size": 200,
  "max_seq_length": 4096,
  "document_separator": "\n\n=== PAPER BREAK ===\n\n"
}
```
